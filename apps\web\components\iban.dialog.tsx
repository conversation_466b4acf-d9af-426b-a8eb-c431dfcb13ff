"use client";

import * as React from "react";
import { useRef, useState } from "react";
import { useForm, revalidateLogic } from "@tanstack/react-form";
import { z } from "zod";
import { Building2, Landmark } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Button } from "@workspace/ui/components/button";
import { FeedbackDialog } from "./feedback.dialog";
import { cn } from "@workspace/ui/lib/utils";

// Turkish IBAN validation schema (TR + 24 digits, checksum validated)
const ibanSchema = z.object({
  iban: z
    .string()
    .min(26, "IBAN gereklidir")
    .regex(/^TR\d{24}$/i, "Geçerli bir TR IBAN giriniz")
    .refine((val) => {
      // Run checksum only when full length is present to avoid lag during typing
      if (!val || val.length < 26) return true;
      // IBAN checksum validation (mod 97 == 1)
      const rearranged = val.slice(4) + val.slice(0, 4);
      const numericString = rearranged.replace(/[A-Z]/g, (c) =>
        String(c.charCodeAt(0) - 55),
      );
      let remainder = 0;
      for (let i = 0; i < numericString.length; i++) {
        remainder = (remainder * 10 + Number(numericString[i])) % 97;
      }
      return remainder === 1;
    }, "Geçersiz IBAN numarası"),
});

export interface IbanDialogProps {
  /** Whether the dialog is open */
  open?: boolean;
  /** Callback when dialog open state changes */
  onOpenChange?: (open: boolean) => void;
  /** Callback when form is submitted successfully */
  onSubmit?: (data: IbanFormData) => void;
  /** Whether to show the close button (X) in the dialog */
  showCloseButton?: boolean;
}

export type IbanFormData = z.infer<typeof ibanSchema>;

// Segmented IBAN helpers
const SEGMENT_SIZES = [2, 4, 4, 4, 4, 4, 2]; // After TR
const joinSegments = (segments: string[]) => `TR${segments.join("")}`;
const splitToSegments = (raw: string) => {
  const cleaned = raw.replace(/\s/g, "").toUpperCase();
  const withoutTR = cleaned.startsWith("TR") ? cleaned.slice(2) : cleaned;
  const result: string[] = [];
  let idx = 0;
  for (const size of SEGMENT_SIZES) {
    result.push(withoutTR.slice(idx, idx + size).replace(/[^0-9]/g, ""));
    idx += size;
  }
  return result;
};

export function IbanDialog({
  open = false,
  onOpenChange,
  onSubmit,
  showCloseButton = true,
}: IbanDialogProps) {
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    defaultValues: {
      iban: "",
    },
    validationLogic: revalidateLogic({
      mode: "submit",
      modeAfterSubmission: "change",
    }),
    validators: {
      onDynamic: ibanSchema,
    },
    onSubmit: async ({ value }) => {
      setIsSubmitting(true);
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500));
        onSubmit?.(value);
        setShowFeedbackDialog(true);
      } catch (error) {
        console.error("IBAN submission failed:", error);
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  // Segmented input state and logic
  const [segments, setSegments] = useState<string[]>(
    Array(SEGMENT_SIZES.length).fill(""),
  );
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const updateFormFromSegments = (next: string[]) => {
    setSegments(next);
    form.setFieldValue("iban", joinSegments(next));
  };

  const handleChange = (index: number, value: string) => {
    const onlyDigits = value.replace(/\D/g, "");
    const max = SEGMENT_SIZES[index];
    const clipped = onlyDigits.slice(0, max);
    const next = [...segments];
    next[index] = clipped;
    updateFormFromSegments(next);
    if (clipped.length === max && index < SEGMENT_SIZES.length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (
      e.key === "Backspace" &&
      (segments[index] ?? "").length === 0 &&
      index > 0
    ) {
      e.preventDefault();
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste: React.ClipboardEventHandler<HTMLDivElement> = (e) => {
    const text = e.clipboardData.getData("text");
    if (!text) return;
    e.preventDefault();
    const filled = splitToSegments(text);
    updateFormFromSegments(filled);
    let lastIdx = 0;
    for (let i = 0; i < filled.length; i++) {
      if (filled[i].length > 0) lastIdx = i;
    }
    inputRefs.current[lastIdx]?.focus();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <DialogContent
            className="sm:max-w-[640px]"
            showCloseButton={showCloseButton}
          >
            <DialogHeader>
              <DialogTitle>{"IBAN Ekleme"}</DialogTitle>
            </DialogHeader>

            {/* IBAN Card Container - mimicking the reference image */}
            <div className="mt-1 mx-auto p-6">
              {/* Bank Icon Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Landmark className="size-10 text-foreground" />
                  <span className="text-sm text-border">{"BANK"}</span>
                </div>
              </div>

              {/* IBAN Input Section */}
              <div className="space-y-4">
                <form.Field name="iban">
                  {(field) => (
                    <div className="space-y-2">
                      <Label
                        htmlFor={field.name}
                        className="text-sm w-fit mx-auto"
                      >
                        {"IBAN NO"}
                      </Label>
                      <div
                        className="flex gap-2 rounded-lg border-2 bg-background p-3 font-mono font-extrabold"
                        onPaste={handlePaste}
                      >
                        <div className="">TR</div>
                        {SEGMENT_SIZES.map((size, idx) => (
                          <div key={idx} className="relative inline-block">
                            <span className="absolute -top-3 left-0 text-[10px] text-muted-foreground pointer-events-none select-none">
                              {size === 2 ? "##" : "####"}
                            </span>
                            <Input
                              ref={(el) => {
                                inputRefs.current[idx] = el;
                              }}
                              inputMode="numeric"
                              pattern="[0-9]*"
                              aria-invalid={
                                field.state.meta.isTouched &&
                                field.state.meta.errors.length > 0
                                  ? true
                                  : undefined
                              }
                              value={segments[idx]}
                              onChange={(e) =>
                                handleChange(idx, e.target.value)
                              }
                              onKeyDown={(e) => handleKeyDown(idx, e)}
                              className={cn(
                                "shrink border-0 p-0 md:text-xl outline-none",
                                size === 2 ? "w-7" : "w-14",
                              )}
                              maxLength={size}
                              size={size}
                              disabled={isSubmitting}
                            />
                          </div>
                        ))}
                      </div>
                      {field.state.meta.isTouched &&
                        field.state.meta.errors.length > 0 && (
                          <p className="text-destructive text-sm">
                            {String(field.state.meta.errors[0]?.message)}
                          </p>
                        )}
                    </div>
                  )}
                </form.Field>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting || !form.state.canSubmit}
                processing={isSubmitting}
              >
                {"KAYDET"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Dialog>

      {/* Feedback Dialog */}
      <FeedbackDialog
        open={showFeedbackDialog}
        onOpenChange={setShowFeedbackDialog}
        title="Başarılı!"
        message="IBAN Eklendi"
        content={[
          "IBAN adresiniz başarıyla sisteme kaydedildi.",
          "Artık bu IBAN adresini para transferleri için kullanabilirsiniz.",
        ]}
        buttonText="TAMAM"
        onButtonClick={() => {
          onOpenChange?.(false);
        }}
        showCloseButton={false}
      />
    </>
  );
}
